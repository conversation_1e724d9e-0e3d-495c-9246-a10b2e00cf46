<?php

namespace App\Http\Controllers\User;

use Exception;
use App\Models\Plan;
use App\Models\Payment;
use Illuminate\Http\Request;
use App\Models\Subscription;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Customer;

class SubscriptionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Show subscription dashboard
     */
    public function index()
    {
        $user = Auth::user();
        $activeSubscription = $user->getCurrentSubscription();
        $subscriptionHistory = $user->subscriptions()->with('plan')->latest()->get();

        return view('user.subscriptions.index', compact(
            'user',
            'activeSubscription',
            'subscriptionHistory'
        ));
    }

    /**
     * Show plan selection page
     */
    public function choosePlan($planId)
    {
        $plan = Plan::where('plans_id', $planId)
                   ->where('status', 1)
                   ->with('services')
                   ->firstOrFail();

        $user = Auth::user();

        // Check if user already has any active subscription
        if ($user->hasActiveSubscription()) {
            $activeSubscription = $user->getCurrentSubscription();
            return redirect()->route('user.subscriptions.index')
                           ->with('error', 'You already have an active subscription (' . $activeSubscription->plan->name . '). Please cancel your current subscription before purchasing a new plan.');
        }

        return view('user.subscriptions.choose-plan', compact('plan', 'user'));
    }

    /**
     * Create Stripe checkout session
     */
    public function createCheckoutSession(Request $request)
    {
        try {
            // Check if Stripe is properly configured
            if (!config('services.stripe.secret') || config('services.stripe.secret') === 'sk_test_your_stripe_secret_key_here') {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment system is not configured. Please contact support.',
                ], 500);
            }

            $request->validate([
                'plan_id' => 'required|exists:plans,plans_id',
            ]);

            $plan = Plan::where('plans_id', $request->plan_id)
                       ->where('status', 1)
                       ->firstOrFail();

            $user = Auth::user();

            // Check if user already has any active subscription
            if ($user->hasActiveSubscription()) {
                $activeSubscription = $user->getCurrentSubscription();
                return response()->json([
                    'success' => false,
                    'message' => 'You already have an active subscription (' . $activeSubscription->plan->name . '). Please cancel your current subscription before purchasing a new plan.',
                ], 400);
            }

            // Create or get Stripe customer
            $stripeCustomer = $this->getOrCreateStripeCustomer($user);

            // Create checkout session
            $session = Session::create([
                'customer' => $stripeCustomer->id,
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => $plan->name,
                            'description' => $plan->description,
                        ],
                        'unit_amount' => $plan->price * 100, // Convert to cents
                        'recurring' => [
                            'interval' => $this->getStripeInterval($plan->duration),
                        ],
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => route('user.subscriptions.success') . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('user.subscriptions.cancel'),
                'metadata' => [
                    'user_id' => $user->users_id,
                    'plan_id' => $plan->plans_id,
                ],
            ]);

            return response()->json([
                'success' => true,
                'checkout_url' => $session->url,
            ]);

        } catch (Exception $e) {
            Log::error('Stripe checkout session creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id(),
                'plan_id' => $request->plan_id ?? null,
                'stripe_configured' => !empty(config('services.stripe.secret')),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unable to create checkout session: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle successful payment
     */
    public function success(Request $request)
    {
        try {
            $sessionId = $request->get('session_id');

            if (!$sessionId) {
                return redirect()->route('pricing')->with('error', 'Invalid session.');
            }

            // Retrieve the session from Stripe
            $session = Session::retrieve($sessionId);

            if ($session->payment_status === 'paid') {
                // Create subscription record
                $this->createSubscriptionFromSession($session);

                return redirect()->route('user.subscriptions.index')
                               ->with('success', 'Subscription activated successfully!');
            }

            return redirect()->route('pricing')
                           ->with('error', 'Payment was not completed successfully.');

        } catch (Exception $e) {
            Log::error('Subscription success handling failed', [
                'error' => $e->getMessage(),
                'session_id' => $request->get('session_id'),
            ]);

            return redirect()->route('pricing')
                           ->with('error', 'There was an issue processing your subscription.');
        }
    }

    /**
     * Handle cancelled payment
     */
    public function cancel()
    {
        return redirect()->route('pricing')
                       ->with('info', 'Subscription cancelled. You can try again anytime.');
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(Request $request)
    {
        try {
            $user = Auth::user();
            $subscription = $user->getCurrentSubscription();

            if (!$subscription) {
                return back()->with('error', 'No active subscription found.');
            }

            // Cancel in Stripe if it exists
            if ($subscription->stripe_subscription_id) {
                $stripeSubscription = Subscription::retrieve($subscription->stripe_subscription_id);
                $stripeSubscription->cancel();
            }

            // Update local subscription
            $subscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'ends_at' => $subscription->current_period_end,
            ]);

            return back()->with('success', 'Subscription cancelled successfully. You can continue using the service until the end of your billing period.');

        } catch (Exception $e) {
            Log::error('Subscription cancellation failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return back()->with('error', 'Unable to cancel subscription. Please contact support.');
        }
    }

    /**
     * Get or create Stripe customer
     */
    private function getOrCreateStripeCustomer($user)
    {
        try {
            // Try to find existing customer
            $customers = Customer::all(['email' => $user->email, 'limit' => 1]);

            if ($customers->data) {
                return $customers->data[0];
            }

            // Create new customer
            return Customer::create([
                'email' => $user->email,
                'name' => $user->name,
                'metadata' => [
                    'user_id' => $user->users_id,
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Stripe customer creation failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->users_id,
            ]);
            throw $e;
        }
    }

    /**
     * Convert plan duration to Stripe interval
     */
    private function getStripeInterval($duration)
    {
        switch (strtolower($duration)) {
            case 'weekly':
                return 'week';
            case 'monthly':
                return 'month';
            case 'yearly':
                return 'year';
            default:
                return 'month';
        }
    }

    /**
     * Create subscription from Stripe session
     */
    private function createSubscriptionFromSession($session)
    {
        $userId = $session->metadata->user_id;
        $planId = $session->metadata->plan_id;

        $user = \App\Models\User::where('users_id', $userId)->firstOrFail();
        $plan = Plan::where('plans_id', $planId)->firstOrFail();

        // Get subscription from Stripe
        $stripeSubscription = \Stripe\Subscription::retrieve($session->subscription);

        // Create local subscription
        $subscription = Subscription::create([
            'user_id' => $userId,
            'plan_id' => $planId,
            'stripe_subscription_id' => $stripeSubscription->id,
            'stripe_customer_id' => $session->customer,
            'status' => 'active',
            'amount' => $plan->price,
            'currency' => 'usd',
            'interval' => $plan->duration,
            'current_period_start' => now()->createFromTimestamp($stripeSubscription->current_period_start),
            'current_period_end' => now()->createFromTimestamp($stripeSubscription->current_period_end),
            'auto_renew' => true,
        ]);

        // Create payment record
        Payment::create([
            'user_id' => $userId,
            'subscription_id' => $subscription->subscription_id,
            'plan_id' => $planId,
            'stripe_payment_intent_id' => $session->payment_intent,
            'amount' => $plan->price,
            'currency' => 'usd',
            'status' => 'succeeded',
            'payment_method' => 'card',
            'description' => "Subscription to {$plan->name}",
            'paid_at' => now(),
        ]);

        return $subscription;
    }
}

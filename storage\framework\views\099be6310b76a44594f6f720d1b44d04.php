

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Subscriptions'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-12 col-md-12">
            <div class="card mb-4">
                <div class="d-flex justify-content-between px-3 py-4">
                    <h6 class="mb-0" style="color: #67748e;">User Subscriptions</h6>
                    <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-sm m-0" style="background-color: #67748e; border-color: #67748e; color: white;">
                        <i class="fas fa-credit-card me-1"></i> View Payments
                    </a>
                </div>

                <div class="card-body p-3">
                    <?php if (isset($component)) { $__componentOriginalc8463834ba515134d5c98b88e1a9dc03 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.data-table','data' => ['id' => 'subscriptionsTable','ajax' => route('admin.subscriptions.index'),'columns' => [
                        [
                            'data' => 'DT_RowIndex',
                            'name' => 'DT_RowIndex',
                            'orderable' => false,
                            'searchable' => false,
                        ],
                        ['data' => 'user_info', 'name' => 'user.name'],
                        ['data' => 'plan_info', 'name' => 'plan.name'],
                        ['data' => 'status_badge', 'name' => 'status'],
                        ['data' => 'period_info', 'name' => 'current_period_start', 'orderable' => false],
                        ['data' => 'auto_renew_status', 'name' => 'auto_renew', 'orderable' => false],
                        ['data' => 'created_at', 'name' => 'created_at'],
                        ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                    ],'order' => [[6, 'desc']]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('data-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'subscriptionsTable','ajax' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.subscriptions.index')),'columns' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        [
                            'data' => 'DT_RowIndex',
                            'name' => 'DT_RowIndex',
                            'orderable' => false,
                            'searchable' => false,
                        ],
                        ['data' => 'user_info', 'name' => 'user.name'],
                        ['data' => 'plan_info', 'name' => 'plan.name'],
                        ['data' => 'status_badge', 'name' => 'status'],
                        ['data' => 'period_info', 'name' => 'current_period_start', 'orderable' => false],
                        ['data' => 'auto_renew_status', 'name' => 'auto_renew', 'orderable' => false],
                        ['data' => 'created_at', 'name' => 'created_at'],
                        ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                    ]),'order' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([[6, 'desc']])]); ?>
                         <?php $__env->slot('header', null, []); ?> 
                            <th>S.No.</th>
                            <th>User</th>
                            <th>Plan</th>
                            <th>Status</th>
                            <th>Period</th>
                            <th>Auto Renew</th>
                            <th>Created</th>
                            <th>Actions</th>
                         <?php $__env->endSlot(); ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $attributes = $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $component = $__componentOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function toggleStatus(subscriptionId) {
    if (confirm('Are you sure you want to toggle the subscription status?')) {
        $.ajax({
            url: `/admin/subscriptions/${subscriptionId}/toggle-status`,
            type: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert(response.message);
                    // Reload the DataTable
                    $('.data-table').DataTable().ajax.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('An error occurred while updating the subscription status.');
            }
        });
    }
}
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle Subscription Status
    $(document).on('click', '.toggle-status', function() {
        const btn = $(this);
        const id = btn.data('id');
        const currentStatus = btn.text().trim();
        const newStatus = currentStatus === 'Active' ? 'Cancelled' : 'Active';

        Swal.fire({
            title: `Change status to ${newStatus}?`,
            text: "Are you sure you want to toggle the subscription status?",
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#aaa',
            confirmButtonText: `Yes, make it ${newStatus}`,
            cancelButtonText: 'Cancel',
            scrollbarPadding: false
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `<?php echo e(route('admin.subscriptions.toggle-status', ':id')); ?>`.replace(':id', id),
                    type: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(res) {
                        if (res.status) {
                            // Reload the DataTable to reflect changes
                            $('#subscriptionsTable').DataTable().ajax.reload(null, false);

                            Swal.fire({
                                title: 'Updated!',
                                text: `Subscription status changed to ${res.newStatus}.`,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false,
                                scrollbarPadding: false
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: res.error || 'Failed to update subscription status.',
                                icon: 'error',
                                timer: 2000,
                                showConfirmButton: false,
                                scrollbarPadding: false
                            });
                        }
                    },
                    error: function(xhr) {
                        const errorMsg = xhr.responseJSON?.error || 'Failed to update subscription status.';
                        Swal.fire({
                            title: 'Error!',
                            text: errorMsg,
                            icon: 'error',
                            timer: 2000,
                            showConfirmButton: false,
                            scrollbarPadding: false
                        });
                    }
                });
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/subscriptions/index.blade.php ENDPATH**/ ?>
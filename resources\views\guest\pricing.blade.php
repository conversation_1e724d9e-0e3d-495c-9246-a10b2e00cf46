@extends('layouts.app')

@section('content')
    <!-- <PERSON> Header -->
    <section class="page-header">
        <div class="container">
            <h1>Pricing Plans</h1>
            <p>Choose the perfect plan for your trash can-to-curb service</p>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section">
        <div class="container">
            @if($plans->count() > 0)
                <div class="pricing-grid">
                    @foreach($plans as $plan)
                        <div class="pricing-card {{ $plan->is_popular ? 'featured' : '' }}">
                            @if($plan->is_popular)
                                <div class="popular-badge">Most Popular</div>
                            @endif

                            <div class="pricing-header">
                                <h3>{{ $plan->name }}</h3>
                                <div class="price">
                                    <span class="currency">$</span>
                                    <span class="amount">{{ number_format($plan->price, 0) }}</span>
                                    <span class="period">/{{ $plan->duration === 'yearly' ? 'year' : ($plan->duration === 'monthly' ? 'month' : 'week') }}</span>
                                </div>
                                {{-- @if($plan->description)
                                    <div class="savings">{{ $plan->description }}</div>
                                @endif --}}
                            </div>

                            <div class="pricing-features">
                                @if($plan->services && $plan->services->count() > 0)
                                    <ul>
                                        @foreach($plan->services as $service)
                                            <li><i class="fas fa-check"></i> {{ $service->name }}</li>
                                        @endforeach
                                    </ul>
                                @else
                                    <ul>
                                        <li><i class="fas fa-check"></i> Standard service included</li>
                                    </ul>
                                @endif
                            </div>

                            <div class="pricing-footer">
                                @auth
                                    @if(auth()->user()->role === '0')
                                        @if(auth()->user()->hasActiveSubscription())
                                            @php
                                                $activeSubscription = auth()->user()->getCurrentSubscription();
                                            @endphp
                                            @if($activeSubscription && $activeSubscription->plan_id === $plan->plans_id)
                                                <button class="btn btn-success" disabled>
                                                    <i class="fas fa-check"></i> Current Plan
                                                </button>
                                            @else
                                                <button class="btn btn-secondary" disabled>
                                                    <i class="fas fa-lock"></i> Already Subscribed
                                                </button>
                                                <small class="text-muted d-block mt-2">
                                                    Cancel current plan to switch
                                                </small>
                                            @endif
                                        @else
                                            <a href="{{ route('user.subscriptions.choose-plan', $plan->plans_id) }}"
                                               class="btn {{ $plan->is_popular ? 'btn-primary' : 'btn-outline' }}">
                                                Choose Plan
                                            </a>
                                        @endif
                                    @else
                                        <button class="btn btn-secondary" disabled>
                                            Admin Account
                                        </button>
                                    @endif
                                @else
                                    <a href="{{ route('login') }}?redirect={{ urlencode(route('user.subscriptions.choose-plan', $plan->plans_id)) }}"
                                       class="btn {{ $plan->is_popular ? 'btn-primary' : 'btn-outline' }}">
                                        Choose Plan
                                    </a>
                                @endauth
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-5">
                    <div class="alert alert-info">
                        <h4>No Plans Available</h4>
                        <p>We're currently updating our pricing plans. Please check back soon!</p>
                    </div>
                </div>
            @endif
        </div>
    </section>
@endsection

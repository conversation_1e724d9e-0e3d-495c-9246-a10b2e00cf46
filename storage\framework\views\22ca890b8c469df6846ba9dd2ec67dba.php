<?php $__env->startSection('content'); ?>
<?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Payment Details'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" style="color: #67748e;">Payment Information</h6>
                        <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment ID</label>
                                <p class="fw-bold"><?php echo e($payment->payment_id); ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Amount</label>
                                <p class="fw-bold text-success">$<?php echo e(number_format($payment->amount, 2)); ?> <?php echo e(strtoupper($payment->currency)); ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <p>
                                    <?php
                                        $statusClass = match($payment->status) {
                                            'succeeded' => 'bg-success',
                                            'pending' => 'bg-warning',
                                            'failed' => 'bg-danger',
                                            'refunded' => 'bg-info',
                                            'cancelled' => 'bg-secondary',
                                            default => 'bg-secondary'
                                        };
                                    ?>
                                    <span class="badge <?php echo e($statusClass); ?>"><?php echo e(ucfirst($payment->status)); ?></span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment Method</label>
                                <p><?php echo e($payment->payment_method ?? 'N/A'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment Date</label>
                                <p><?php echo e($payment->paid_at ? $payment->paid_at->format('M d, Y g:i A') : 'N/A'); ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <p><?php echo e($payment->created_at->format('M d, Y g:i A')); ?></p>
                            </div>
                            <?php if($payment->failed_at): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">Failed At</label>
                                <p class="text-danger"><?php echo e($payment->failed_at->format('M d, Y g:i A')); ?></p>
                            </div>
                            <?php endif; ?>
                            <?php if($payment->refunded_at): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">Refunded At</label>
                                <p class="text-info"><?php echo e($payment->refunded_at->format('M d, Y g:i A')); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if($payment->description): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Description</label>
                                <p><?php echo e($payment->description); ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($payment->refunded_amount && $payment->refunded_amount > 0): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <strong>Refund Information:</strong> $<?php echo e(number_format($payment->refunded_amount, 2)); ?> has been refunded.
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- User Information -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">User Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Name</label>
                        <p class="fw-bold"><?php echo e($payment->user->name); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Email</label>
                        <p><?php echo e($payment->user->email); ?></p>
                    </div>
                    <?php if($payment->user->mobile): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">Mobile</label>
                        <p><?php echo e($payment->user->mobile); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Plan Information -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Plan Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Plan Name</label>
                        <p class="fw-bold"><?php echo e($payment->plan->name); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Plan Price</label>
                        <p>$<?php echo e(number_format($payment->plan->price, 2)); ?></p>
                    </div>
                    <?php if($payment->plan->description): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">Description</label>
                        <p class="text-sm"><?php echo e($payment->plan->description); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Subscription Information -->
            <?php if($payment->subscription): ?>
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Related Subscription</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Subscription ID</label>
                        <p>
                            <a href="<?php echo e(route('admin.subscriptions.show', $payment->subscription->subscription_id)); ?>" class="text-decoration-none">
                                <?php echo e(substr($payment->subscription->subscription_id, 0, 8)); ?>...
                            </a>
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Status</label>
                        <p>
                            <?php
                                $statusClass = match($payment->subscription->status) {
                                    'active' => 'bg-success',
                                    'cancelled' => 'bg-warning',
                                    'expired' => 'bg-danger',
                                    'past_due' => 'bg-warning',
                                    default => 'bg-secondary'
                                };
                            ?>
                            <span class="badge <?php echo e($statusClass); ?>"><?php echo e(ucfirst($payment->subscription->status)); ?></span>
                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Stripe Information -->
    <?php if($payment->stripe_payment_intent_id || $payment->stripe_charge_id): ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Stripe Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php if($payment->stripe_payment_intent_id): ?>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment Intent ID</label>
                                <p class="font-monospace"><?php echo e($payment->stripe_payment_intent_id); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if($payment->stripe_charge_id): ?>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Charge ID</label>
                                <p class="font-monospace"><?php echo e($payment->stripe_charge_id); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if($payment->stripe_response && is_array($payment->stripe_response)): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Stripe Response</label>
                                <pre class="bg-light p-3 rounded text-sm"><?php echo e(json_encode($payment->stripe_response, JSON_PRETTY_PRINT)); ?></pre>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/payments/show.blade.php ENDPATH**/ ?>
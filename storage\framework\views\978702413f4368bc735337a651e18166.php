<?php $__env->startSection('content'); ?>
<?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Subscription Details'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" style="color: #67748e;">Subscription Information</h6>
                        <a href="<?php echo e(route('admin.subscriptions.index')); ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Subscription ID</label>
                                <p class="fw-bold"><?php echo e($subscription->subscription_id); ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <p>
                                    <?php
                                        $statusClass = match($subscription->status) {
                                            'active' => 'bg-success',
                                            'cancelled' => 'bg-warning',
                                            'expired' => 'bg-danger',
                                            'past_due' => 'bg-warning',
                                            default => 'bg-secondary'
                                        };
                                    ?>
                                    <span class="badge <?php echo e($statusClass); ?>"><?php echo e(ucfirst($subscription->status)); ?></span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Amount</label>
                                <p class="fw-bold">$<?php echo e(number_format($subscription->amount, 2)); ?> <?php echo e(strtoupper($subscription->currency)); ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Billing Interval</label>
                                <p><?php echo e(ucfirst($subscription->interval)); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Current Period</label>
                                <p>
                                    <?php echo e($subscription->current_period_start ? $subscription->current_period_start->format('M d, Y') : 'N/A'); ?> - 
                                    <?php echo e($subscription->current_period_end ? $subscription->current_period_end->format('M d, Y') : 'N/A'); ?>

                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Auto Renew</label>
                                <p>
                                    <?php if($subscription->auto_renew): ?>
                                        <i class="fas fa-check text-success"></i> Enabled
                                    <?php else: ?>
                                        <i class="fas fa-times text-danger"></i> Disabled
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <p><?php echo e($subscription->created_at->format('M d, Y g:i A')); ?></p>
                            </div>
                            <?php if($subscription->cancelled_at): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">Cancelled At</label>
                                <p class="text-warning"><?php echo e($subscription->cancelled_at->format('M d, Y g:i A')); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- User Information -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">User Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Name</label>
                        <p class="fw-bold"><?php echo e($subscription->user->name); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Email</label>
                        <p><?php echo e($subscription->user->email); ?></p>
                    </div>
                    <?php if($subscription->user->mobile): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">Mobile</label>
                        <p><?php echo e($subscription->user->mobile); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Plan Information -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Plan Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Plan Name</label>
                        <p class="fw-bold"><?php echo e($subscription->plan->name); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Plan Price</label>
                        <p>$<?php echo e(number_format($subscription->plan->price, 2)); ?></p>
                    </div>
                    <?php if($subscription->plan->description): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">Description</label>
                        <p class="text-sm"><?php echo e($subscription->plan->description); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Recent Payments</h6>
                </div>
                <div class="card-body">
                    <?php if($recentPayments->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Payment ID</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Payment Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <small class="text-muted"><?php echo e(substr($payment->payment_id, 0, 8)); ?>...</small>
                                        </td>
                                        <td>$<?php echo e(number_format($payment->amount, 2)); ?></td>
                                        <td>
                                            <?php
                                                $statusClass = match($payment->status) {
                                                    'succeeded' => 'bg-success',
                                                    'pending' => 'bg-warning',
                                                    'failed' => 'bg-danger',
                                                    'refunded' => 'bg-info',
                                                    'cancelled' => 'bg-secondary',
                                                    default => 'bg-secondary'
                                                };
                                            ?>
                                            <span class="badge <?php echo e($statusClass); ?>"><?php echo e(ucfirst($payment->status)); ?></span>
                                        </td>
                                        <td><?php echo e($payment->paid_at ? $payment->paid_at->format('M d, Y') : 'N/A'); ?></td>
                                        <td>
                                            <a href="<?php echo e(route('admin.payments.show', $payment->payment_id)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No payments found for this subscription.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/subscriptions/show.blade.php ENDPATH**/ ?>
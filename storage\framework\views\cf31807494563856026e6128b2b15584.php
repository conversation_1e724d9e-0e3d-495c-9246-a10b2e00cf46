

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Pricing Plans</h1>
            <p>Choose the perfect plan for your trash can-to-curb service</p>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section">
        <div class="container">
            <?php if($plans->count() > 0): ?>
                <div class="pricing-grid">
                    <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="pricing-card <?php echo e($plan->is_popular ? 'featured' : ''); ?>">
                            <?php if($plan->is_popular): ?>
                                <div class="popular-badge">Most Popular</div>
                            <?php endif; ?>

                            <div class="pricing-header">
                                <h3><?php echo e($plan->name); ?></h3>
                                <div class="price">
                                    <span class="currency">$</span>
                                    <span class="amount"><?php echo e(number_format($plan->price, 0)); ?></span>
                                    <span class="period">/<?php echo e($plan->duration === 'yearly' ? 'year' : ($plan->duration === 'monthly' ? 'month' : 'week')); ?></span>
                                </div>
                                
                            </div>

                            <div class="pricing-features">
                                <?php if($plan->services && $plan->services->count() > 0): ?>
                                    <ul>
                                        <?php $__currentLoopData = $plan->services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><i class="fas fa-check"></i> <?php echo e($service->name); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                <?php else: ?>
                                    <ul>
                                        <li><i class="fas fa-check"></i> Standard service included</li>
                                    </ul>
                                <?php endif; ?>
                            </div>

                            <div class="pricing-footer">
                                <?php if(auth()->guard()->check()): ?>
                                    <?php if(auth()->user()->role === '0'): ?>
                                        <?php if(auth()->user()->hasActiveSubscription()): ?>
                                            <?php
                                                $activeSubscription = auth()->user()->getCurrentSubscription();
                                            ?>
                                            <?php if($activeSubscription && $activeSubscription->plan_id === $plan->plans_id): ?>
                                                <button class="btn btn-success" disabled>
                                                    <i class="fas fa-check"></i> Current Plan
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-secondary" disabled>
                                                    <i class="fas fa-lock"></i> Already Subscribed
                                                </button>
                                                <small class="text-muted d-block mt-2">
                                                    Cancel current plan to switch
                                                </small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <a href="<?php echo e(route('user.subscriptions.choose-plan', $plan->plans_id)); ?>"
                                               class="btn <?php echo e($plan->is_popular ? 'btn-primary' : 'btn-outline'); ?>">
                                                Choose Plan
                                            </a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <button class="btn btn-secondary" disabled>
                                            Admin Account
                                        </button>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <a href="<?php echo e(route('login')); ?>?redirect=<?php echo e(urlencode(route('user.subscriptions.choose-plan', $plan->plans_id))); ?>"
                                       class="btn <?php echo e($plan->is_popular ? 'btn-primary' : 'btn-outline'); ?>">
                                        Choose Plan
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="alert alert-info">
                        <h4>No Plans Available</h4>
                        <p>We're currently updating our pricing plans. Please check back soon!</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/guest/pricing.blade.php ENDPATH**/ ?>
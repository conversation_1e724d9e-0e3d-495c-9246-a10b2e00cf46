<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Payment;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;

class WebhookController extends Controller
{
    public function __construct()
    {
        // Set Stripe API key
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Handle Stripe webhook events
     */
    public function handleStripeWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook_secret');

        try {
            // Verify webhook signature
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (SignatureVerificationException $e) {
            Log::error('Stripe webhook signature verification failed', [
                'error' => $e->getMessage()
            ]);
            return response('Invalid signature', 400);
        } catch (Exception $e) {
            Log::error('Stripe webhook error', [
                'error' => $e->getMessage()
            ]);
            return response('Webhook error', 400);
        }

        // Handle the event
        try {
            switch ($event['type']) {
                case 'invoice.payment_succeeded':
                    $this->handleInvoicePaymentSucceeded($event['data']['object']);
                    break;

                case 'invoice.payment_failed':
                    $this->handleInvoicePaymentFailed($event['data']['object']);
                    break;

                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdated($event['data']['object']);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event['data']['object']);
                    break;

                default:
                    Log::info('Unhandled Stripe webhook event', [
                        'type' => $event['type']
                    ]);
            }

            return response('Webhook handled', 200);

        } catch (Exception $e) {
            Log::error('Stripe webhook handling failed', [
                'event_type' => $event['type'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response('Webhook handling failed', 500);
        }
    }

    /**
     * Handle successful invoice payment (renewals)
     */
    private function handleInvoicePaymentSucceeded($invoice)
    {
        if (!$invoice['subscription']) {
            return; // Not a subscription invoice
        }

        $subscription = Subscription::where('stripe_subscription_id', $invoice['subscription'])->first();
        
        if (!$subscription) {
            Log::warning('Subscription not found for invoice payment', [
                'stripe_subscription_id' => $invoice['subscription']
            ]);
            return;
        }

        // Update subscription period
        $stripeSubscription = \Stripe\Subscription::retrieve($invoice['subscription']);
        
        $subscription->update([
            'current_period_start' => now()->createFromTimestamp($stripeSubscription->current_period_start),
            'current_period_end' => now()->createFromTimestamp($stripeSubscription->current_period_end),
            'status' => 'active'
        ]);

        // Create payment record
        Payment::create([
            'payment_id' => (string) \Illuminate\Support\Str::uuid(),
            'user_id' => $subscription->user_id,
            'subscription_id' => $subscription->subscription_id,
            'plan_id' => $subscription->plan_id,
            'stripe_payment_intent_id' => $invoice['payment_intent'],
            'amount' => $invoice['amount_paid'] / 100, // Convert from cents
            'currency' => $invoice['currency'],
            'status' => 'succeeded',
            'payment_method' => 'card',
            'description' => 'Subscription renewal',
            'paid_at' => now()->createFromTimestamp($invoice['status_transitions']['paid_at']),
        ]);

        Log::info('Subscription renewed successfully', [
            'subscription_id' => $subscription->subscription_id,
            'amount' => $invoice['amount_paid'] / 100
        ]);
    }

    /**
     * Handle failed invoice payment
     */
    private function handleInvoicePaymentFailed($invoice)
    {
        if (!$invoice['subscription']) {
            return;
        }

        $subscription = Subscription::where('stripe_subscription_id', $invoice['subscription'])->first();
        
        if (!$subscription) {
            return;
        }

        // Create failed payment record
        Payment::create([
            'payment_id' => (string) \Illuminate\Support\Str::uuid(),
            'user_id' => $subscription->user_id,
            'subscription_id' => $subscription->subscription_id,
            'plan_id' => $subscription->plan_id,
            'amount' => $invoice['amount_due'] / 100,
            'currency' => $invoice['currency'],
            'status' => 'failed',
            'payment_method' => 'card',
            'description' => 'Subscription renewal failed',
            'failed_at' => now(),
        ]);

        Log::warning('Subscription payment failed', [
            'subscription_id' => $subscription->subscription_id,
            'amount' => $invoice['amount_due'] / 100
        ]);
    }

    /**
     * Handle subscription updates
     */
    private function handleSubscriptionUpdated($stripeSubscription)
    {
        $subscription = Subscription::where('stripe_subscription_id', $stripeSubscription['id'])->first();
        
        if (!$subscription) {
            return;
        }

        $subscription->update([
            'status' => $stripeSubscription['status'] === 'active' ? 'active' : 'cancelled',
            'current_period_start' => now()->createFromTimestamp($stripeSubscription['current_period_start']),
            'current_period_end' => now()->createFromTimestamp($stripeSubscription['current_period_end']),
        ]);
    }

    /**
     * Handle subscription deletion
     */
    private function handleSubscriptionDeleted($stripeSubscription)
    {
        $subscription = Subscription::where('stripe_subscription_id', $stripeSubscription['id'])->first();
        
        if (!$subscription) {
            return;
        }

        $subscription->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'ends_at' => now()->createFromTimestamp($stripeSubscription['current_period_end']),
        ]);
    }
}

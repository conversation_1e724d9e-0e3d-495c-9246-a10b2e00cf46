<style>
    /* Container and Layout */
    .subscription-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Header Styles */
    .subscription-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        flex-wrap: wrap;
        gap: 20px;
    }

    .subscription-title {
        margin: 0;
        color: #1e3a8a;
        font-weight: 700;
        font-size: 2rem;
    }

    .subscription-subtitle {
        color: #6b7280;
        margin: 5px 0 0 0;
        font-size: 1rem;
    }

    /* Button Styles */
    .btn-custom {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 12px 24px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
    }

    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        border: 2px solid #dc3545;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        color: white;
    }

    /* Alert Styles */
    .alert {
        padding: 15px 20px;
        margin-bottom: 20px;
        border-radius: 15px;
        border: none;
        display: flex;
        align-items: center;
        gap: 10px;
        position: relative;
    }

    .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border-left: 4px solid #28a745;
        color: #155724;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-left: 4px solid #dc3545;
        color: #721c24;
    }

    .alert-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        border-left: 4px solid #17a2b8;
        color: #0c5460;
    }

    .alert-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        position: absolute;
        right: 15px;
        top: 15px;
        opacity: 0.7;
    }

    .alert-close:hover {
        opacity: 1;
    }

    /* Card Styles */
    .card {
        background: white;
        border: none;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        padding: 20px;
        border-bottom: none;
        font-weight: 700;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .card-header-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    .card-header-default {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        color: #1e3a8a;
    }

    .card-body {
        padding: 30px;
    }

    /* Subscription Info Styles */
    .subscription-info {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        gap: 15px;
    }

    .subscription-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
    }

    .subscription-details h4 {
        margin: 0 0 5px 0;
        color: #1e3a8a;
        font-weight: 700;
        font-size: 1.5rem;
    }

    .subscription-details p {
        margin: 0;
        color: #6b7280;
    }

    /* Info Grid */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .info-box {
        background: #f8fafc;
        padding: 15px;
        border-radius: 12px;
        border-left: 4px solid #3b82f6;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .info-box:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .info-box-success {
        border-left-color: #10b981;
    }

    .info-box-warning {
        border-left-color: #f59e0b;
    }

    .info-box-primary {
        border-left-color: #3b82f6;
    }

    .info-content small {
        display: block;
        color: #6b7280;
        font-size: 0.8rem;
        margin-bottom: 2px;
    }

    .info-content strong {
        color: #1e3a8a;
        font-weight: 600;
    }

    /* Badge Styles */
    .badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .badge-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    .badge-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
    }

    .badge-danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
    }

    .badge-info {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
    }

    /* Auto Renew Notice */
    .auto-renew-notice {
        margin-top: 20px;
        padding: 15px;
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        border-radius: 12px;
        border: 1px solid #a7f3d0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .auto-renew-notice i {
        color: #10b981;
    }

    .auto-renew-notice small {
        color: #059669;
        font-weight: 600;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 50px 30px;
    }

    .empty-state-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 20px;
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 40px;
        color: #9ca3af;
    }

    .empty-state h4 {
        color: #1e3a8a;
        font-weight: 700;
        margin-bottom: 15px;
    }

    .empty-state p {
        color: #6b7280;
        font-size: 16px;
        max-width: 400px;
        margin: 0 auto 25px;
        line-height: 1.6;
    }

    /* Utility Classes */
    .text-center {
        text-align: center;
    }

    .text-end {
        text-align: right;
    }

    .text-muted {
        color: #6b7280;
    }

    .text-success {
        color: #10b981;
    }

    .text-primary {
        color: #3b82f6;
    }

    .text-warning {
        color: #f59e0b;
    }

    .text-danger {
        color: #ef4444;
    }

    .mb-0 {
        margin-bottom: 0;
    }

    .mb-1 {
        margin-bottom: 5px;
    }

    .mb-3 {
        margin-bottom: 15px;
    }

    .mb-4 {
        margin-bottom: 20px;
    }

    .mt-4 {
        margin-top: 20px;
    }

    .me-2 {
        margin-right: 10px;
    }

    .fw-semibold {
        font-weight: 600;
    }

    .d-flex {
        display: flex;
    }

    .align-items-center {
        align-items: center;
    }

    .justify-content-between {
        justify-content: space-between;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .subscription-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .subscription-title {
            font-size: 1.5rem;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .card-body {
            padding: 20px;
        }

        .subscription-info {
            flex-direction: column;
            text-align: center;
        }
    }
</style>

<?php $__env->startSection('content'); ?>
    <div class="subscription-container">
        <!-- Header Section -->
        <div class="subscription-header">
            <div>
                <h2 class="subscription-title">
                    <i class="fas fa-crown" style="color: #f59e0b;"></i> My Subscriptions
                </h2>
                <p class="subscription-subtitle">Manage your subscription plans and payment history</p>
            </div>
            <a href="<?php echo e(route('pricing')); ?>" class="btn-custom">
                <i class="fas fa-plus"></i> Browse Plans
            </a>
        </div>

        <!-- Alert Messages -->
        <?php if(session('success')): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i><?php echo e(session('success')); ?>

                <button type="button" class="alert-close"
                    onclick="this.parentElement.style.display='none'">&times;</button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i><?php echo e(session('error')); ?>

                <button type="button" class="alert-close"
                    onclick="this.parentElement.style.display='none'">&times;</button>
            </div>
        <?php endif; ?>

        <?php if(session('info')): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i><?php echo e(session('info')); ?>

                <button type="button" class="alert-close"
                    onclick="this.parentElement.style.display='none'">&times;</button>
            </div>
        <?php endif; ?>

        <!-- Current Subscription -->
        <?php if($activeSubscription): ?>
            <div class="card">
                <div class="card-header card-header-success">
                    <i class="fas fa-check-circle"></i> Current Subscription
                </div>
                <div class="card-body">
                    <div
                        style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 20px;">
                        <div style="flex: 1; min-width: 300px;">
                            <div class="subscription-info">
                                <div class="subscription-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="subscription-details">
                                    <h4><?php echo e($activeSubscription->plan->name); ?></h4>
                                    <p><?php echo e($activeSubscription->plan->description ?? 'Premium subscription plan'); ?></p>
                                </div>
                            </div>

                            <div class="info-grid">
                                <div class="info-box info-box-primary">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                    <div class="info-content">
                                        <small>Amount</small>
                                        <strong>$<?php echo e(number_format($activeSubscription->amount, 2)); ?></strong>
                                    </div>
                                </div>
                                <div class="info-box info-box-success">
                                    <i class="fas fa-calendar text-success"></i>
                                    <div class="info-content">
                                        <small>Billing</small>
                                        <strong><?php echo e(ucfirst($activeSubscription->interval ?? 'monthly')); ?></strong>
                                    </div>
                                </div>
                                <div class="info-box info-box-success">
                                    <i class="fas fa-check-circle text-success"></i>
                                    <div class="info-content">
                                        <small>Status</small>
                                        <span class="badge badge-success"><?php echo e(ucfirst($activeSubscription->status)); ?></span>
                                    </div>
                                </div>
                                <div class="info-box info-box-warning">
                                    <i class="fas fa-clock text-warning"></i>
                                    <div class="info-content">
                                        <small>Next Billing</small>
                                        <strong><?php echo e($activeSubscription->current_period_end ? $activeSubscription->current_period_end->format('M d, Y') : 'N/A'); ?></strong>
                                    </div>
                                </div>
                            </div>

                            <?php if($activeSubscription->auto_renew ?? true): ?>
                                <div class="auto-renew-notice">
                                    <i class="fas fa-sync"></i>
                                    <small>Auto-renewal is enabled - Your subscription will renew automatically</small>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="text-end">
                            <?php if($activeSubscription->isActive()): ?>
                                <form method="POST" action="<?php echo e(route('user.subscriptions.cancel-subscription')); ?>"
                                    onsubmit="return confirm('Are you sure you want to cancel your subscription? You will continue to have access until the end of your billing period.')">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn-danger">
                                        <i class="fas fa-times me-2"></i> Cancel Subscription
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-body empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h4>No Active Subscription</h4>
                    <p>You don't have any active subscription. Browse our premium plans to unlock exclusive features and get
                        started!</p>
                    <a href="<?php echo e(route('pricing')); ?>" class="btn-custom">
                        <i class="fas fa-eye"></i> View Plans
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Subscription History -->
        <?php if($subscriptionHistory->count() > 0): ?>
            <div class="card">
                <div class="card-header card-header-default">
                    <i class="fas fa-history"></i> Subscription History
                </div>
                <div class="card-body">
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                                    <th style="padding: 15px; text-align: left; color: #1e3a8a; font-weight: 600;">Plan</th>
                                    <th style="padding: 15px; text-align: left; color: #1e3a8a; font-weight: 600;">Amount
                                    </th>
                                    <th style="padding: 15px; text-align: left; color: #1e3a8a; font-weight: 600;">Status
                                    </th>
                                    <th style="padding: 15px; text-align: left; color: #1e3a8a; font-weight: 600;">Period
                                    </th>
                                    <th style="padding: 15px; text-align: left; color: #1e3a8a; font-weight: 600;">Created
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $subscriptionHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr style="border-bottom: 1px solid #f1f5f9; transition: all 0.3s ease;"
                                        onmouseover="this.style.backgroundColor='#f8fafc'"
                                        onmouseout="this.style.backgroundColor='white'">
                                        <td style="padding: 15px; vertical-align: middle;">
                                            <strong><?php echo e($subscription->plan->name); ?></strong><br>
                                            <small class="text-muted"><?php echo e(ucfirst($subscription->interval)); ?></small>
                                        </td>
                                        <td style="padding: 15px; vertical-align: middle;">
                                            $<?php echo e(number_format($subscription->amount, 2)); ?></td>
                                        <td style="padding: 15px; vertical-align: middle;">
                                            <?php
                                                $statusClass = match ($subscription->status) {
                                                    'active' => 'badge-success',
                                                    'cancelled' => 'badge-warning',
                                                    'expired' => 'badge-danger',
                                                    default => 'badge-info',
                                                };
                                            ?>
                                            <span
                                                class="badge <?php echo e($statusClass); ?>"><?php echo e(ucfirst($subscription->status)); ?></span>
                                        </td>
                                        <td style="padding: 15px; vertical-align: middle;">
                                            <?php if($subscription->current_period_start && $subscription->current_period_end): ?>
                                                <?php echo e($subscription->current_period_start->format('M d')); ?> -
                                                <?php echo e($subscription->current_period_end->format('M d, Y')); ?>

                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </td>
                                        <td style="padding: 15px; vertical-align: middle;">
                                            <?php echo e($subscription->created_at->format('M d, Y')); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/user/subscriptions/index.blade.php ENDPATH**/ ?>
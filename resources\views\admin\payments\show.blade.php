@extends('layouts.app')

@section('content')
@include('layouts.navbars.auth.topnav', ['title' => 'Payment Details'])

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" style="color: #67748e;">Payment Information</h6>
                        <a href="{{ route('admin.payments.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment ID</label>
                                <p class="fw-bold">{{ $payment->payment_id }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Amount</label>
                                <p class="fw-bold text-success">${{ number_format($payment->amount, 2) }} {{ strtoupper($payment->currency) }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <p>
                                    @php
                                        $statusClass = match($payment->status) {
                                            'succeeded' => 'bg-success',
                                            'pending' => 'bg-warning',
                                            'failed' => 'bg-danger',
                                            'refunded' => 'bg-info',
                                            'cancelled' => 'bg-secondary',
                                            default => 'bg-secondary'
                                        };
                                    @endphp
                                    <span class="badge {{ $statusClass }}">{{ ucfirst($payment->status) }}</span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment Method</label>
                                <p>{{ $payment->payment_method ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment Date</label>
                                <p>{{ $payment->paid_at ? $payment->paid_at->format('M d, Y g:i A') : 'N/A' }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <p>{{ $payment->created_at->format('M d, Y g:i A') }}</p>
                            </div>
                            @if($payment->failed_at)
                            <div class="mb-3">
                                <label class="form-label text-muted">Failed At</label>
                                <p class="text-danger">{{ $payment->failed_at->format('M d, Y g:i A') }}</p>
                            </div>
                            @endif
                            @if($payment->refunded_at)
                            <div class="mb-3">
                                <label class="form-label text-muted">Refunded At</label>
                                <p class="text-info">{{ $payment->refunded_at->format('M d, Y g:i A') }}</p>
                            </div>
                            @endif
                        </div>
                    </div>
                    
                    @if($payment->description)
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Description</label>
                                <p>{{ $payment->description }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($payment->refunded_amount && $payment->refunded_amount > 0)
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <strong>Refund Information:</strong> ${{ number_format($payment->refunded_amount, 2) }} has been refunded.
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- User Information -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">User Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Name</label>
                        <p class="fw-bold">{{ $payment->user->name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Email</label>
                        <p>{{ $payment->user->email }}</p>
                    </div>
                    @if($payment->user->mobile)
                    <div class="mb-3">
                        <label class="form-label text-muted">Mobile</label>
                        <p>{{ $payment->user->mobile }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Plan Information -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Plan Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Plan Name</label>
                        <p class="fw-bold">{{ $payment->plan->name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Plan Price</label>
                        <p>${{ number_format($payment->plan->price, 2) }}</p>
                    </div>
                    @if($payment->plan->description)
                    <div class="mb-3">
                        <label class="form-label text-muted">Description</label>
                        <p class="text-sm">{{ $payment->plan->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Subscription Information -->
            @if($payment->subscription)
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Related Subscription</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Subscription ID</label>
                        <p>
                            <a href="{{ route('admin.subscriptions.show', $payment->subscription->subscription_id) }}" class="text-decoration-none">
                                {{ substr($payment->subscription->subscription_id, 0, 8) }}...
                            </a>
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Status</label>
                        <p>
                            @php
                                $statusClass = match($payment->subscription->status) {
                                    'active' => 'bg-success',
                                    'cancelled' => 'bg-warning',
                                    'expired' => 'bg-danger',
                                    'past_due' => 'bg-warning',
                                    default => 'bg-secondary'
                                };
                            @endphp
                            <span class="badge {{ $statusClass }}">{{ ucfirst($payment->subscription->status) }}</span>
                        </p>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Stripe Information -->
    @if($payment->stripe_payment_intent_id || $payment->stripe_charge_id)
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Stripe Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if($payment->stripe_payment_intent_id)
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment Intent ID</label>
                                <p class="font-monospace">{{ $payment->stripe_payment_intent_id }}</p>
                            </div>
                        </div>
                        @endif
                        @if($payment->stripe_charge_id)
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Charge ID</label>
                                <p class="font-monospace">{{ $payment->stripe_charge_id }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                    
                    @if($payment->stripe_response && is_array($payment->stripe_response))
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Stripe Response</label>
                                <pre class="bg-light p-3 rounded text-sm">{{ json_encode($payment->stripe_response, JSON_PRETTY_PRINT) }}</pre>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

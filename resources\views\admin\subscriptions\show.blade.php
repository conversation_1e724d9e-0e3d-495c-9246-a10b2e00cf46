@extends('layouts.app')

@section('content')
@include('layouts.navbars.auth.topnav', ['title' => 'Subscription Details'])

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" style="color: #67748e;">Subscription Information</h6>
                        <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Subscription ID</label>
                                <p class="fw-bold">{{ $subscription->subscription_id }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <p>
                                    @php
                                        $statusClass = match($subscription->status) {
                                            'active' => 'bg-success',
                                            'cancelled' => 'bg-warning',
                                            'expired' => 'bg-danger',
                                            'past_due' => 'bg-warning',
                                            default => 'bg-secondary'
                                        };
                                    @endphp
                                    <span class="badge {{ $statusClass }}">{{ ucfirst($subscription->status) }}</span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Amount</label>
                                <p class="fw-bold">${{ number_format($subscription->amount, 2) }} {{ strtoupper($subscription->currency) }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Billing Interval</label>
                                <p>{{ ucfirst($subscription->interval) }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Current Period</label>
                                <p>
                                    {{ $subscription->current_period_start ? $subscription->current_period_start->format('M d, Y') : 'N/A' }} - 
                                    {{ $subscription->current_period_end ? $subscription->current_period_end->format('M d, Y') : 'N/A' }}
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Auto Renew</label>
                                <p>
                                    @if($subscription->auto_renew)
                                        <i class="fas fa-check text-success"></i> Enabled
                                    @else
                                        <i class="fas fa-times text-danger"></i> Disabled
                                    @endif
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <p>{{ $subscription->created_at->format('M d, Y g:i A') }}</p>
                            </div>
                            @if($subscription->cancelled_at)
                            <div class="mb-3">
                                <label class="form-label text-muted">Cancelled At</label>
                                <p class="text-warning">{{ $subscription->cancelled_at->format('M d, Y g:i A') }}</p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- User Information -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">User Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Name</label>
                        <p class="fw-bold">{{ $subscription->user->name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Email</label>
                        <p>{{ $subscription->user->email }}</p>
                    </div>
                    @if($subscription->user->mobile)
                    <div class="mb-3">
                        <label class="form-label text-muted">Mobile</label>
                        <p>{{ $subscription->user->mobile }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Plan Information -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Plan Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Plan Name</label>
                        <p class="fw-bold">{{ $subscription->plan->name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Plan Price</label>
                        <p>${{ number_format($subscription->plan->price, 2) }}</p>
                    </div>
                    @if($subscription->plan->description)
                    <div class="mb-3">
                        <label class="form-label text-muted">Description</label>
                        <p class="text-sm">{{ $subscription->plan->description }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0" style="color: #67748e;">Recent Payments</h6>
                </div>
                <div class="card-body">
                    @if($recentPayments->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Payment ID</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Payment Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentPayments as $payment)
                                    <tr>
                                        <td>
                                            <small class="text-muted">{{ substr($payment->payment_id, 0, 8) }}...</small>
                                        </td>
                                        <td>${{ number_format($payment->amount, 2) }}</td>
                                        <td>
                                            @php
                                                $statusClass = match($payment->status) {
                                                    'succeeded' => 'bg-success',
                                                    'pending' => 'bg-warning',
                                                    'failed' => 'bg-danger',
                                                    'refunded' => 'bg-info',
                                                    'cancelled' => 'bg-secondary',
                                                    default => 'bg-secondary'
                                                };
                                            @endphp
                                            <span class="badge {{ $statusClass }}">{{ ucfirst($payment->status) }}</span>
                                        </td>
                                        <td>{{ $payment->paid_at ? $payment->paid_at->format('M d, Y') : 'N/A' }}</td>
                                        <td>
                                            <a href="{{ route('admin.payments.show', $payment->payment_id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">No payments found for this subscription.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

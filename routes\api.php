<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebhookController;

// Stripe webhook (no auth required)
Route::post('/stripe/webhook', [WebhookController::class, 'handleStripeWebhook'])->name('stripe.webhook');

// Test webhook endpoint (for development only)
Route::get('/stripe/webhook/test', function() {
    return response()->json([
        'status' => 'success',
        'message' => 'Webhook endpoint is accessible',
        'timestamp' => now()->toISOString()
    ]);
});

// Publiclly accessible routes

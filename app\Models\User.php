<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use Notifiable, HasFactory;

    protected $primaryKey = 'users_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'mobile',
        'image',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected static function booted(): void
    {
        static::creating(function ($user) {
            if (empty($user->users_id)) {
                $user->users_id = (string) Str::uuid();
            }
        });
    }

    // Relationships for subscriptions
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'user_id', 'users_id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'user_id', 'users_id');
    }

    public function activeSubscription()
    {
        return $this->hasOne(Subscription::class, 'user_id', 'users_id')
            ->where('status', 'active')
            ->latest();
    }

    // Helper methods for subscriptions
    public function hasActiveSubscription()
    {
        return $this->activeSubscription()->exists();
    }

    public function getCurrentSubscription()
    {
        return $this->activeSubscription()->first();
    }

    public function isSubscribedTo($planId)
    {
        return $this->subscriptions()
            ->where('plan_id', $planId)
            ->where('status', 'active')
            ->exists();
    }
}
